name: flutter_tools
description: Tools for building Flutter applications
homepage: https://flutter.dev

environment:
  sdk: ^3.7.0-0

dependencies:
  # To update these, use "flutter update-packages --force-upgrade".
  #
  # For detailed instructions, refer to:
  # https://github.com/flutter/flutter/blob/main/docs/infra/Updating-dependencies-in-Flutter.md
  archive: 3.6.1
  args: 2.7.0
  dds: 5.0.3
  dwds: 24.3.10
  code_builder: 4.10.1
  completion: 1.0.1
  coverage: 1.11.1
  crypto: 3.0.6
  ffi: 2.1.4
  file: 7.0.1
  flutter_template_images: 5.0.0
  html: 0.15.5+1
  http: 1.3.0
  intl: 0.20.2
  meta: 1.16.0
  multicast_dns: 0.3.3
  mustache_template: 2.0.0
  package_config: 2.2.0
  process: 5.0.3
  fake_async: 1.3.3
  stack_trace: 1.12.1
  usage: 4.1.1
  webdriver: 3.1.0
  webkit_inspection_protocol: 1.2.1
  xml: 6.5.0
  yaml: 3.1.3
  native_stack_traces: 0.6.0
  shelf: 1.4.2
  vm_snapshot_analysis: 0.7.6
  uuid: 4.5.1
  web_socket_channel: 3.0.2
  stream_channel: 2.1.4
  shelf_web_socket: 2.0.1
  shelf_static: 1.1.3
  pub_semver: 2.2.0
  pool: 1.5.1
  path: 1.9.1
  mime: 2.0.0
  logging: 1.3.0
  http_multi_server: 3.2.2
  convert: 3.1.2
  async: 2.13.0
  unified_analytics: 7.0.2

  graphs: 2.3.2
  native_assets_builder: 0.13.0
  native_assets_cli: 0.13.0

  # We depend on very specific internal implementation details of the
  # 'test' package, which change between versions, so when upgrading
  # this, make sure the tests are still running correctly.
  test_api: 0.7.4
  test_core: 0.6.8

  vm_service: 15.0.0

  standard_message_codec: 0.0.1+4

  _fe_analyzer_shared: 80.0.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  analyzer: 7.3.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  boolean_selector: 2.1.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  browser_launcher: 1.1.3 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  built_collection: 5.1.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  built_value: 8.10.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  clock: 1.1.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  csslib: 1.0.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  dap: 1.4.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  dart_service_protocol_shared: 0.0.3 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  dds_service_extensions: 2.0.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  devtools_shared: 12.0.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  dtd: 4.0.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  extension_discovery: 2.1.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  fixnum: 1.1.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  frontend_server_client: 4.0.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  glob: 2.1.3 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  http_parser: 4.1.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  io: 1.0.5 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  json_rpc_2: 4.0.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  matcher: 0.12.17 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  petitparser: 6.1.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  platform: 3.1.6 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  shelf_packages_handler: 3.0.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  shelf_proxy: 1.0.4 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  source_map_stack_trace: 2.1.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  source_maps: 0.10.13 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  source_span: 1.10.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  sprintf: 7.0.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  sse: 4.1.8 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  string_scanner: 1.4.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  sync_http: 0.3.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  term_glyph: 1.2.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  typed_data: 1.4.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  vm_service_interface: 2.0.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  watcher: 1.1.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  web: 1.1.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  web_socket: 0.1.6 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  yaml_edit: 2.2.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"

dev_dependencies:
  collection: 1.19.1
  file_testing: 3.0.2
  pubspec_parse: 1.5.0

  checked_yaml: 2.0.3 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  js: 0.7.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  json_annotation: 4.9.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  node_preamble: 2.0.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  test: 1.25.15 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"

dartdoc:
  # Exclude this package from the hosted API docs.
  nodoc: true

# PUBSPEC CHECKSUM: 36ae
