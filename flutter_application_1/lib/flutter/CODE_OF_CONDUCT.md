<!-- when updating this file also update https://github.com/flutter/.github/blob/main/CODE_OF_CONDUCT.md -->

# Code of conduct

The Flutter project expects Flutter's contributors to act professionally
and respectfully. Flutter contributors are expected to maintain the safety
and dignity of Flutter's social environments (such as GitHub and Discord).

Specifically:

* Respect people, their identities, their culture, and their work.
* Be kind. Be courteous. Be welcoming.
* Listen. Consider and acknowledge people's points before responding.

Should you experience anything that makes you feel unwelcome in Flutter's
community, please contact [<EMAIL>](mailto:<EMAIL>)
or, if you prefer, directly contact someone on the project, for instance
[Hixi<PERSON>](mailto:<EMAIL>).

The Flutter project will not tolerate harassment in <PERSON><PERSON><PERSON>'s
community, even outside of Flutter's public communication channels.

## Conflict resolution

When multiple contributors disagree on the direction for a particular
patch or the general direction of the project, the conflict should be
resolved by communication. The people who disagree should get
together, try to understand each other's points of view, and work to
find a design that addresses everyone's concerns.

This is usually sufficient to resolve issues. If you cannot come to an
agreement, ask for the advice of a more senior member of the project.

Be wary of agreement by attrition, where one person argues a point
repeatedly until other participants give up in the interests of moving
on. This is not conflict resolution, as it does not address everyone's
concerns. Be wary of agreement by compromise, where two good competing
solutions are merged into one mediocre solution. A conflict is
addressed when the participants agree that the final solution is
_better_ than all the conflicting proposals. Sometimes the solution is
more work than either of the proposals. [Embrace the yak shave](./docs/contributing/Style-guide-for-Flutter-repo.md#lazy-programming).

## Questions

It's always ok to ask questions. Our systems are large, and nobody will be
an expert in all the systems. Once you find the answer, document it in
the first place you looked. That way, the next person will be brought
up to speed even quicker.

!["I try not to make fun of people for admitting they don't know things, because for each thing 'everyone knows' by the time they're adults, every day there are, on average, 10,000 people in the US hearing about it for the first time. If I make fun of people, I train them not to tell me when they have those moments. And I miss out on the fun." "Diet coke and mentos thing? What's that?" "Oh, man! We're going to the grocery store." "Why?" "You're one of today's lucky 10,000."](https://imgs.xkcd.com/comics/ten_thousand.png)

Source: _[xkcd, May 2012](https://xkcd.com/1053/)_
