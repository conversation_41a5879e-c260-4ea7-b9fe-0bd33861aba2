# Below is a list of Flutter team members' GitHub handles who are
# suggested reviewers for contributions to this repository.
#
# These names are just suggestions. It is fine to have your changes
# reviewed by someone else.
#
# Use git ls-files '<pattern>' without a / prefix to see the list of matching files.

/engine/src/build/config/ios/** @vashworth
/packages/flutter_tools/templates/module/ios/ @vashworth
/packages/flutter_tools/templates/**/Podfile* @vashworth
/packages/flutter_tools/lib/src/ios/ @vashworth

# Internal dev infra that is closely tied to team-infra.
/bin/internal/update_engine_version.ps1 @matanlurey
/bin/internal/update_engine_version.sh @matanlurey
/dev/bots/** @matanlurey
/dev/conductor/** @matanlurey
/dev/devicelab/** @matanlurey
/dev/tools/test/update_engine_version_test.dart @matanlurey

# flutter_drver, integration_test. Intentionally omit root files (i.e. pubspec.yaml upgrades).
/dev/tools/android_driver_extensions @matanlurey
/packages/flutter_driver/lib/** @matanlurey
/packages/flutter_driver/test/** @matanlurey
/packages/integration_test/lib/** @matanlurey
/packages/integration_test/test/** @matanlurey

# The following files define an Application Binary Interface (ABI) that must maintain
# both forward and backward compatibility. Changes should be heavily
# scrutinized as mistakes are irreversible.
/engine/src/flutter/shell/platform/embedder/embedder.h @cbracken @chinmaygarde @loic-sharma
/engine/src/flutter/shell/platform/embedder/tests/embedder_frozen.h @cbracken @chinmaygarde @loic-sharma

# iOS team - keep this synced with .github/labeler.yml's team-ios section.
/engine/src/flutter/shell/platform/darwin/common/ @flutter/ios-reviewers
/engine/src/flutter/shell/platform/darwin/ios/framework/ @flutter/ios-reviewers
/packages/flutter_tools/**/ios/* @flutter/ios-reviewers
/packages/flutter_tools/**/macos/* @flutter/ios-reviewers
/packages/flutter_tools/**/*xcode* @flutter/ios-reviewers
/packages/flutter_tools/**/*ios* @flutter/ios-reviewers
/packages/flutter_tools/**/*macos* @flutter/ios-reviewers
