import 'package:flutter/material.dart';
import 'package:flutter_application_1/dice_roller.dart';

const startAlignment = Alignment.centerRight;
const endAlignment = Alignment.topLeft;

// make a custom widget
class GradientContainer extends StatelessWidget {
  const GradientContainer(this.color1, this.color2, {super.key});
  final Color color1;
  final Color color2;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color1, color2],
          begin: startAlignment,
          end: endAlignment,
        ),
      ),
      child: const Center(child: <PERSON><PERSON><PERSON>oller()),
    );
  }
}
